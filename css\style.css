html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}
body {
  background-color: #020f22;
  color: #c0c0c0;
  margin: 0;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

header {
  width: 100%;
  height: 10vh;
  background: url(../assets/Title.webp) no-repeat center center;
  background-size: 100% 100%;
}
.main-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-grow: 1;
}

.charts-row {
  display: flex;
  gap: 10px;
  width: 100%;
  flex-wrap: nowrap;
  flex: 1; /* 让行容器占据可用空间 */
}

.balance-chart-layout {
  width: 60%;
  height: 100%;
  background: url(../assets/BalanceBg.webp) no-repeat center center;
  background-size: 100% 100%;
}
.balance-chart {
  height: 100%;
}
.charts-column {
  display: flex;
  flex-direction: column;
  width: 40%;
  gap: 10px;
}

.chart-container,
.table-container,
.info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;
  position: relative;
}

/* 移除不再使用的large-chart样式 */
/* .large-chart {
    grid-column: span 2;
} */

/* 断面监视表格样式 */
.section-table {
  width: 60%;
}

/* 右侧列样式 */
.right-column {
  width: 40%;
}

.chart-title {
  color: #fff;
  font-size: 1.2em;
  margin-bottom: 10px;
  border-left: 4px solid #00aeff;
  padding-left: 10px;
}

.chart {
  flex: 1; /* 让图表占据剩余空间 */
}

#section-table table {
  width: 100%;
  border-collapse: collapse;
}

#section-table th,
#section-table td {
  border: 1px solid #1a3a66;
  padding: 8px;
  text-align: center;
}

#section-table th {
  background-color: #1a3a66;
  color: #fff;
}

#cps-chart {
  flex: 2;
}
.info-container {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

.info-box {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.current-cost {
  position: relative;
  width: 45%;
  height: 50%;
  background: url(../assets/CurrentCost.webp) no-repeat center center;
  background-size: contain;
}

.real-time-ace {
  position: relative;
  width: 45%;
  height: 50%;
  background: url(../assets/RealTimeACE.webp) no-repeat center center;
  background-size: contain;
}

.info-box-value {
  position: absolute;
  bottom: 15%;
  left: 60%;
  transform: translateX(-50%);
  font-size: 1.5em;
  color: #fff;
  font-weight: bold;
}

/* 数据标签样式 */
.data-label {
  position: absolute;
  background-color: rgba(10, 26, 51, 0.7);
  border: 1px solid #1a3a66;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
  color: #fff;
  pointer-events: none;
}

/* 当前值标记样式 */
.current-value {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid;
}
